import hashlib
import json
import time
from flask import Flask, jsonify, request
from paddleocr import PaddleOCR

app = Flask(__name__)


ocr = PaddleOCR(
		use_doc_orientation_classify = False,
		use_doc_unwarping = False,
		use_textline_orientation = False)

def sb_img(img_path):
	time1 = time.time()
	result = ocr.predict(img_path)
	save_path = "./output/res_{}.json".format(hashlib.md5(img_path.encode(encoding='UTF-8')).hexdigest())
	for res in result:
		res.save_to_json(save_path=save_path)
	time2 = time.time()
	print("time: ", time2 - time1)
	with open(save_path, "r+") as f:
		return {"code": 200, "msg": "success", "data": json.load(f)}



@app.route("/", methods=["POST", "GET"])
def hello_world():
	request_data = request.query_string
	print(request_data)
	img_path = "/Volumes/APP/test/3624774a98241be10699f1092dc759.PNG"
	# result = ocr.predict(img_path)
	# save_path = "./output/res_{}.json".format(md5(img_path))
	return sb_img(img_path)

# if __name__ == "__main__":
	# app.run(host="0.0.0.0", port=5000)
	# imgPath = "/Volumes/APP/test/3624774a98241be10699f1092dc759.PNG"
	# print(sb_img(imgPath))

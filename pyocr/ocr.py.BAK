from paddleocr import PaddleOCR

imgPath = "/Users/<USER>/Pictures/d616fef9-4ed6-479d-9234-7db85c4c19e7_m9wf488k_1745563329937.jpg"
ocr = PaddleOCR(
    use_doc_orientation_classify=False,
    use_doc_unwarping=False,
    use_textline_orientation=False)
result = ocr.predict(imgPath)
# (list)result存到json
with open("runtime/result.json", "w") as f:
    f.write(str(result))
print(result)

# for res in result:
#     res.print()
#     res.save_to_img("output")
#     res.save_to_json("output")